# 自动运行模式OPT拍照时序问题修复说明

## 问题描述

用户反馈的问题：**自动运行模式下OPT的设置的时序问题，当用户点开始运行时如果当前步骤在第二步拍照步骤，设备还没运行到指定位置就会拍一张，这张是实时画面里获取到的，这不对**。

## 问题根源分析

### 1. 时序竞争条件
- **触发模式设置时机**：用户点击开始自动运行时，系统立即设置OPT相机为触发模式
- **设备移动时间**：设备需要时间移动到指定位置
- **拍照时机冲突**：在设备还未到达指定位置时，可能就触发了拍照，导致拍摄的是实时画面而非正确位置的图像

### 2. 原有延迟保护不足
- **触发模式延迟**：原来只有1.5秒的延迟保护
- **设备移动时间**：实际设备移动可能需要更长时间
- **缺乏位置检测**：没有真正检测设备是否已到达指定位置

## 修复方案

### 1. 增强触发模式延迟保护

**文件**: `IPM.Vision\ViewModel\Pages\VisionPageViewModel.cs`

```csharp
// 修复前
private readonly TimeSpan _triggerModeDelay = TimeSpan.FromMilliseconds(1500); // 触发模式延迟1.5秒

// 修复后
private readonly TimeSpan _triggerModeDelay = TimeSpan.FromMilliseconds(2500); // 触发模式延迟2.5秒，增加等待时间确保设备到位
```

### 2. 新增设备位置到达检测机制

#### 2.1 添加设备位置等待状态变量

```csharp
// 设备位置到达检测相关
private bool _isWaitingForDevicePosition = false; // 是否正在等待设备到达指定位置
private DateTime _deviceMoveStartTime = DateTime.MinValue; // 设备开始移动的时间
private readonly TimeSpan _maxDeviceMoveWaitTime = TimeSpan.FromSeconds(10); // 最大等待设备移动时间10秒
```

#### 2.2 添加设备就绪检测属性

```csharp
/// <summary>
/// 检查设备是否已到达指定位置并准备好拍照
/// </summary>
private bool IsDeviceReadyForCapture
{
    get
    {
        // 如果不在等待设备位置状态，说明设备已就绪
        if (!_isWaitingForDevicePosition) return true;
        
        // 如果等待时间超过最大限制，强制认为设备已就绪（避免无限等待）
        if (_deviceMoveStartTime != DateTime.MinValue && 
            DateTime.Now - _deviceMoveStartTime > _maxDeviceMoveWaitTime)
        {
            NotifyLog($"⚠️ 设备移动等待超时({_maxDeviceMoveWaitTime.TotalSeconds}秒)，强制认为设备已就绪");
            _isWaitingForDevicePosition = false;
            return true;
        }
        
        return false;
    }
}
```

### 3. 修改步骤切换逻辑

#### 3.1 设备移动命令发送时标记等待状态

在 `HandleProcessChangeInAutoModeSafely` 方法中：

```csharp
// **新增功能：标记设备开始移动，等待设备到达指定位置**
_isWaitingForDevicePosition = true;
_deviceMoveStartTime = DateTime.Now;
NotifyLog($"🚶 设备开始移动到新位置，等待设备到达指定位置...");

await _equipmentService.SetT(newProcess.EquipmentPara?.T ?? 0);
_equipmentService.SetZAsync(newProcess.EquipmentPara?.Z ?? 0);
await _equipmentService.SetR(newProcess.EquipmentPara?.R ?? 0);

// 动态计算包含加速减速点的坐标数组
var (xArrayWithAccelDecel, yArrayWithAccelDecel) = CalculateCoordinatesWithAccelDecelForRuntime(newProcess.PinPositions);
await _equipmentService.WriteXYArray(xArrayWithAccelDecel, yArrayWithAccelDecel, xArrayWithAccelDecel.Length);

NotifyLog($"📡 设备移动命令已发送，等待设备状态就绪通知...");
```

#### 3.2 设备状态就绪时清除等待状态

在 `_equipmentService_McStatusNotify` 方法中：

```csharp
// **新增功能：设备状态就绪时，标记设备已到达指定位置**
if (_isWaitingForDevicePosition)
{
    var waitTime = _deviceMoveStartTime != DateTime.MinValue ? 
        DateTime.Now - _deviceMoveStartTime : TimeSpan.Zero;
    NotifyLog($"✅ 设备已到达指定位置 (等待时间: {waitTime.TotalSeconds:F1}秒)");
    _isWaitingForDevicePosition = false;
    _deviceMoveStartTime = DateTime.MinValue;
}
```

### 4. 修改OPT照片处理逻辑

在 `_optVisionService_UpdatePictureModelEvent` 方法中增加设备位置检测：

```csharp
// **新增功能：设备位置到达检测，防止设备未到位就拍照**
if (!IsDeviceReadyForCapture)
{
    var waitTime = _deviceMoveStartTime != DateTime.MinValue ? 
        DateTime.Now - _deviceMoveStartTime : TimeSpan.Zero;
    // **使用BeginInvoke避免阻塞**
    Application.Current?.Dispatcher.BeginInvoke(new Action(() =>
        NotifyLog($"🚶 设备尚未到达指定位置，忽略照片 - {obj.FileName} (已等待: {waitTime.TotalSeconds:F1}秒)")));
    return;
}
```

### 5. 状态重置机制

#### 5.1 自动运行开始时重置状态

```csharp
// **新增功能：重置设备位置等待状态**
_isWaitingForDevicePosition = false;
_deviceMoveStartTime = DateTime.MinValue;
NotifyLog("🔄 已重置设备位置等待状态");
```

#### 5.2 强制停止时重置状态

```csharp
// **新增功能：重置设备位置等待状态**
_isWaitingForDevicePosition = false;
_deviceMoveStartTime = DateTime.MinValue;
NotifyLog("🔄 强制停止：已重置设备位置等待状态");
```

## 工作流程

### 修复后的完整流程

1. **用户点击开始自动运行**：
   - 立即设置OPT相机触发模式
   - 启动2.5秒延迟保护
   - 重置设备位置等待状态

2. **步骤切换时**：
   - 发送设备移动命令（SetT, SetZ, SetR, WriteXYArray）
   - 标记 `_isWaitingForDevicePosition = true`
   - 记录设备移动开始时间

3. **设备移动期间**：
   - OPT相机可能接收到PLC触发信号
   - 但照片处理会被 `IsDeviceReadyForCapture` 检测拦截
   - 记录日志并忽略这些"过早"的照片

4. **设备到达指定位置**：
   - PLC发送状态码2（设备就绪）
   - 系统接收到状态通知，清除等待状态
   - 后续OPT照片可以正常处理

5. **超时保护**：
   - 如果10秒内设备仍未就绪，强制认为设备已到位
   - 避免系统无限等待

## 修复效果

### ✅ 解决的问题

1. **防止过早拍照**：确保只有设备到达指定位置后才处理OPT照片
2. **增强时序控制**：通过设备状态检测实现精确的时序控制
3. **提高拍照准确性**：避免拍摄实时画面，确保拍摄正确位置的图像
4. **增加超时保护**：防止设备故障导致的无限等待

### 📊 关键参数

- **触发模式延迟**：2.5秒（从1.5秒增加）
- **最大设备移动等待时间**：10秒
- **设备状态防抖延迟**：500ms（保持不变）

### 🔍 调试信息

系统会记录详细的日志信息：
- 设备移动开始时间
- 设备到达位置的等待时间
- 被忽略的"过早"照片
- 设备移动超时警告

## 注意事项

1. **兼容性**：修复保持了原有的触发模式保持策略，不影响其他功能
2. **性能**：增加的检测逻辑对性能影响极小
3. **可靠性**：增加了多重保护机制，提高系统稳定性
4. **可维护性**：所有新增功能都有详细的日志记录，便于调试和维护
